using UnityEngine;
using ToolSelection;
using Inventory;
using KinematicCharacterController.FPS;

public class ToolSelectionController : MonoBehaviour
{
    [Header("References")]
    [SerializeField] private ToolSelectionManager toolSelectionManager;
    [SerializeField] private ToolModelManager toolModelManager;
    [SerializeField] private GrapplingHookSystem grapplingHookSystem;
    [SerializeField] private ClimbingRopeSystem climbingRopeSystem; // Add reference to rope system
    [SerializeField] private FPSCharacterCamera fpsCamera;

    private ToolSelectionManager.ToolSelectionItem activeTool;
    private Item activeToolItem;

    void Start()
    {
        // Find tool selection manager if not assigned
        if (toolSelectionManager == null)
        {
            toolSelectionManager = FindObjectOfType<ToolSelectionManager>();
            if (toolSelectionManager == null)
            {
                Debug.LogError("ToolSelectionController: No ToolSelectionManager found in scene!");
                return;
            }
        }

        // Find tool model manager if not assigned
        if (toolModelManager == null)
        {
            toolModelManager = GetComponent<ToolModelManager>();
            if (toolModelManager == null)
            {
                Debug.LogWarning("ToolSelectionController: No ToolModelManager found on this GameObject!");
            }
        }

        // Find grappling hook system if not assigned
        if (grapplingHookSystem == null)
        {
            grapplingHookSystem = GetComponent<GrapplingHookSystem>();
        }

        // Find climbing rope system if not assigned
        if (climbingRopeSystem == null)
        {
            climbingRopeSystem = GetComponent<ClimbingRopeSystem>();
        }

        // Register for tool selection changes
        if (toolSelectionManager != null)
        {
            toolSelectionManager.OnToolSelected += OnToolSelected;
        }
    }

    private void OnToolSelected(ToolSelectionManager.ToolSelectionItem selectedTool)
    {
        activeTool = selectedTool;
        activeToolItem = selectedTool.originalItem;

        // ToolModelManager will handle the model and animation changes automatically
        // through its own event handler for OnToolSelected

        bool isGrapplingHookSelected = false;
        bool isClimbingRopeSelected = false;
        bool isHandSelected = false;

        // Check what type of tool is selected
        if (activeToolItem != null)
        {
            var typeField = activeToolItem.GetType().GetField("Type");
            if (typeField != null && typeField.FieldType.IsEnum)
            {
                object toolType = typeField.GetValue(activeToolItem);
                string typeName = toolType.ToString();

                // Be specific with checks to avoid conflicts
                isGrapplingHookSelected = typeName == "GrapplingHookMk1" || typeName.Contains("GrapplingHook");
                isClimbingRopeSelected = typeName == "ClimbingRope" || typeName.EndsWith("ClimbingRope");
                isHandSelected = typeName.Contains("Hand") || typeName.Contains("Empty");
            }
        }
        else
        {
            // If no tool is selected, consider it as hand selected
            isHandSelected = true;
        }

        // Enable or disable grappling hook prediction based on selection
        if (grapplingHookSystem != null)
        {
            grapplingHookSystem.SetPredictionEnabled(isGrapplingHookSelected);
        }

        // Enable or disable climbing rope prediction based on selection
        if (climbingRopeSystem != null)
        {
            climbingRopeSystem.SetPredictionEnabled(isClimbingRopeSelected);
        }

        // Enable or disable zoom capability based on hand selection
        if (fpsCamera != null)
        {
            fpsCamera.SetCanZoom(isHandSelected);
        }
    }

    void Update()
    {
        // Handle tool usage input (left mouse click)
        if (Input.GetMouseButtonDown(0) && activeTool != null)
        {
            UseTool();
        }

        // Release grappling hook/rope with right mouse click
        if (Input.GetMouseButtonDown(1) && activeTool != null)
        {
            ReleaseToolAction();
        }
    }

    private void UseTool()
{
    // Play use animation via the ToolModelManager
    if (toolModelManager != null)
    {
        toolModelManager.PlayUseAnimation();
    }
    
    // This is where you would implement the tool usage functionality
    Debug.Log($"Using tool: {activeTool.toolName}");
    
    // Get the Type field if it exists
    if (activeToolItem != null)
    {
        var typeField = activeToolItem.GetType().GetField("Type");
        if (typeField != null && typeField.FieldType.IsEnum)
        {
            object toolType = typeField.GetValue(activeToolItem);
            Debug.Log($"Tool type: {toolType}");
            
            // Handle different tool types - BE SPECIFIC WITH THE CHECKS
            string typeName = toolType.ToString();
            
            // Check for climbing rope FIRST (more specific)
            if (typeName == "ClimbingRope" || typeName.EndsWith("ClimbingRope"))
            {
                Debug.Log($"Climbing rope detected! System null: {climbingRopeSystem == null}, IsAttached: {climbingRopeSystem?.IsRopeAttached}");
                // Climbing rope logic - fire the rope
                if (climbingRopeSystem != null && !climbingRopeSystem.IsRopeAttached)
                {
                    Debug.Log("Firing climbing rope!");
                    climbingRopeSystem.FireGrapple();
                }
                else if (climbingRopeSystem == null)
                {
                    Debug.LogError("ClimbingRopeSystem is null! Make sure it's on the player GameObject.");
                }
            }
            else if (typeName == "ClimbingAxe" || typeName.EndsWith("ClimbingAxe"))
            {
                // Climbing axe logic
                Debug.Log("Using climbing axe");
            }
            else if (typeName == "GrapplingHookMk1" || typeName.Contains("GrapplingHook"))
            {
                // Grappling hook logic - fire the hook
                if (grapplingHookSystem != null && !grapplingHookSystem.IsSwinging)
                {
                    grapplingHookSystem.FireGrapple();
                }
            }
        }
    }
}

private void ReleaseToolAction()
{
    // Get the Type field if it exists
    if (activeToolItem != null)
    {
        var typeField = activeToolItem.GetType().GetField("Type");
        if (typeField != null && typeField.FieldType.IsEnum)
        {
            object toolType = typeField.GetValue(activeToolItem);
            string typeName = toolType.ToString();
            
            // Check for climbing rope FIRST (more specific)
            if (typeName == "ClimbingRope" || typeName.EndsWith("ClimbingRope"))
            {
                // Release climbing rope
                if (climbingRopeSystem != null && climbingRopeSystem.IsRopeAttached)
                {
                    climbingRopeSystem.ReleaseGrapple();
                }
            }
            else if (typeName == "GrapplingHookMk1" || typeName.Contains("GrapplingHook"))
            {
                // Release grappling hook
                if (grapplingHookSystem != null && grapplingHookSystem.IsSwinging)
                {
                    grapplingHookSystem.ReleaseGrapple();
                }
            }
        }
    }
}

    void OnDestroy()
    {
        // Unregister from tool selection changes
        if (toolSelectionManager != null)
        {
            toolSelectionManager.OnToolSelected -= OnToolSelected;
        }
    }
}