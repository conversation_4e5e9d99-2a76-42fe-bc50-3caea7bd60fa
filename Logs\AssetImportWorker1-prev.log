Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.1f1 (7197418f847b) revision 7444289'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-07-30T21:46:23Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.1.1f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker1.log
-srvPort
62220
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [102308]  Target information:

Player connection [102308]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1870294777 [EditorId] 1870294777 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [102308]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1870294777 [EditorId] 1870294777 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [102308]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1870294777 [EditorId] 1870294777 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [102308]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1870294777 [EditorId] 1870294777 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [102308]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1870294777 [EditorId] 1870294777 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [102308] Host joined multi-casting on [***********:54997]...
Player connection [102308] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 432.32 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.1f1 (7197418f847b)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.1.1f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.7688
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56120
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.011247 seconds.
- Loaded All Assemblies, in  1.323 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.127 seconds
Domain Reload Profiling: 3425ms
	BeginReloadAssembly (612ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (106ms)
	RebuildNativeTypeToScriptingClass (71ms)
	initialDomainReloadingComplete (162ms)
	LoadAllAssembliesAndSetupDomain (346ms)
		LoadAssemblies (566ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (340ms)
			TypeCache.Refresh (335ms)
				TypeCache.ScanAssembly (302ms)
			BuildScriptInfoCaches (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (2128ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1994ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (120ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (354ms)
			ProcessInitializeOnLoadAttributes (1280ms)
			ProcessInitializeOnLoadMethodAttributes (230ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Launched and connected shader compiler UnityShaderCompiler.exe after 0.06 seconds
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.425 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 32.69 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.739 seconds
Domain Reload Profiling: 9096ms
	BeginReloadAssembly (3025ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (171ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (135ms)
	RebuildCommonClasses (191ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (226ms)
	LoadAllAssembliesAndSetupDomain (1883ms)
		LoadAssemblies (1869ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (649ms)
			TypeCache.Refresh (504ms)
				TypeCache.ScanAssembly (468ms)
			BuildScriptInfoCaches (119ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (3740ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3389ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (425ms)
			ProcessInitializeOnLoadAttributes (2104ms)
			ProcessInitializeOnLoadMethodAttributes (809ms)
			AfterProcessingInitializeOnLoad (39ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (54ms)
Refreshing native plugins compatible for Editor in 36.01 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 549 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7910 unused Assets / (3.2 MB). Loaded Objects now: 8973.
Memory consumption went from 377.3 MB to 374.1 MB.
Total: 25.226400 ms (FindLiveObjects: 1.288200 ms CreateObjectMapping: 1.472400 ms MarkObjects: 15.559600 ms  DeleteObjects: 6.904700 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.293 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 28.47 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.779 seconds
Domain Reload Profiling: 6021ms
	BeginReloadAssembly (533ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (44ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (75ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (137ms)
	LoadAllAssembliesAndSetupDomain (2476ms)
		LoadAssemblies (2126ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (620ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (589ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (2780ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2103ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (14ms)
			BeforeProcessingInitializeOnLoad (594ms)
			ProcessInitializeOnLoadAttributes (1090ms)
			ProcessInitializeOnLoadMethodAttributes (336ms)
			AfterProcessingInitializeOnLoad (62ms)
			EditorAssembliesLoaded (5ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (117ms)
Refreshing native plugins compatible for Editor in 22.10 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7906 unused Assets / (4.4 MB). Loaded Objects now: 8996.
Memory consumption went from 314.7 MB to 310.3 MB.
Total: 19.248600 ms (FindLiveObjects: 1.328000 ms CreateObjectMapping: 0.946200 ms MarkObjects: 12.882100 ms  DeleteObjects: 4.091000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.200 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 43.44 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.107 seconds
Domain Reload Profiling: 4257ms
	BeginReloadAssembly (529ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (72ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (98ms)
	RebuildCommonClasses (85ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (117ms)
	LoadAllAssembliesAndSetupDomain (1387ms)
		LoadAssemblies (1113ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (498ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (466ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (2108ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1332ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (371ms)
			ProcessInitializeOnLoadAttributes (744ms)
			ProcessInitializeOnLoadMethodAttributes (177ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (51ms)
Refreshing native plugins compatible for Editor in 26.08 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7906 unused Assets / (4.9 MB). Loaded Objects now: 8998.
Memory consumption went from 311.5 MB to 306.7 MB.
Total: 18.008400 ms (FindLiveObjects: 1.283300 ms CreateObjectMapping: 1.576200 ms MarkObjects: 10.014900 ms  DeleteObjects: 5.132500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.109 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.53 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.691 seconds
Domain Reload Profiling: 4693ms
	BeginReloadAssembly (594ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (54ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (147ms)
	RebuildCommonClasses (95ms)
	RebuildNativeTypeToScriptingClass (34ms)
	initialDomainReloadingComplete (261ms)
	LoadAllAssembliesAndSetupDomain (2017ms)
		LoadAssemblies (1852ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (407ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (371ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1692ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1304ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (329ms)
			ProcessInitializeOnLoadAttributes (781ms)
			ProcessInitializeOnLoadMethodAttributes (157ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (50ms)
Refreshing native plugins compatible for Editor in 22.84 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7906 unused Assets / (5.9 MB). Loaded Objects now: 9000.
Memory consumption went from 311.5 MB to 305.7 MB.
Total: 14.472600 ms (FindLiveObjects: 1.435300 ms CreateObjectMapping: 1.041700 ms MarkObjects: 8.545600 ms  DeleteObjects: 3.448600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.950 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 107.63 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.622 seconds
Domain Reload Profiling: 7532ms
	BeginReloadAssembly (593ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (48ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (205ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (122ms)
	LoadAllAssembliesAndSetupDomain (2110ms)
		LoadAssemblies (1584ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (709ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (669ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (4623ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3777ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (675ms)
			ProcessInitializeOnLoadAttributes (2675ms)
			ProcessInitializeOnLoadMethodAttributes (314ms)
			AfterProcessingInitializeOnLoad (97ms)
			EditorAssembliesLoaded (5ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (215ms)
Refreshing native plugins compatible for Editor in 49.84 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7906 unused Assets / (3.8 MB). Loaded Objects now: 9002.
Memory consumption went from 311.6 MB to 307.8 MB.
Total: 25.042400 ms (FindLiveObjects: 1.583700 ms CreateObjectMapping: 1.844500 ms MarkObjects: 15.822800 ms  DeleteObjects: 5.789600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 196230.178464 seconds.
  path: Assets/_Game/Models/Items/Manual.fbx
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Items/Manual.fbx using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ce0e01cff9fe3e238b5dad10036fb1d4') in 8.4492451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 232.366966 seconds.
  path: Assets/Samples/High Definition RP/Common/Prefabs/Light/HDRPLightFixture.prefab
  artifactKey: Guid(1ebc340953a0a5c478502bfd77a4e56f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/High Definition RP/Common/Prefabs/Light/HDRPLightFixture.prefab using Guid(1ebc340953a0a5c478502bfd77a4e56f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '692fe9a0667da11f09236b774407b88f') in 0.0068822 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Samples/High Definition RP/Common/Prefabs/Light/HDRPSamplesSpotlight.prefab
  artifactKey: Guid(aeee657e48a483143b51801a678d4933) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/High Definition RP/Common/Prefabs/Light/HDRPSamplesSpotlight.prefab using Guid(aeee657e48a483143b51801a678d4933) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '319d778faaa22ab8f6a12a8bdd64d377') in 0.0054827 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 39

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Samples/Core RP Library/Common/Prefabs/UnityBall/UnityMaterialBall_Gold.prefab
  artifactKey: Guid(45917c7d5543b774894963f1abc77d43) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/Core RP Library/Common/Prefabs/UnityBall/UnityMaterialBall_Gold.prefab using Guid(45917c7d5543b774894963f1abc77d43) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '031879822aac9f342869f55cee3dae61') in 0.0096982 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 52

========================================================================
Received Import Request.
  Time since last request: 181.289590 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_arch_diff.mat
  artifactKey: Guid(a10985de58869ec49b002b490d3e7bc4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_arch_diff.mat using Guid(a10985de58869ec49b002b490d3e7bc4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9b267b2c69447fde4c35b9f668629547') in 0.12287 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_column_c_diff.mat
  artifactKey: Guid(fe397fa5e276ea043a9b99331fa6c925) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_column_c_diff.mat using Guid(fe397fa5e276ea043a9b99331fa6c925) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ecd872854ab841a440195055922285a2') in 0.1029348 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_curtain_blue_diff.mat
  artifactKey: Guid(ed53b87934592b74e9f0e0343fc5c70d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_curtain_blue_diff.mat using Guid(ed53b87934592b74e9f0e0343fc5c70d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd56bbdfc7cf7a022e11404f0d2606e5c') in 0.1099097 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9ec51b62831d34afe2c703b589044def') in 0.2025493 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-HDRP LIT.shadergraph
  artifactKey: Guid(ca2ed216f98028c4dae6c5224a952b3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-HDRP LIT.shadergraph using Guid(ca2ed216f98028c4dae6c5224a952b3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bad0e72398aa8440332704e76e46e79a') in 52.3427478 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/_Game/Materials/GRIDPNG/Red/Materials/texture_10.mat
  artifactKey: Guid(35fc78f1482860347acb040baf50d10e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Materials/GRIDPNG/Red/Materials/texture_10.mat using Guid(35fc78f1482860347acb040baf50d10e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'def2e0e146c9f3f76288e54f83a3c1bd') in 0.7817742 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0