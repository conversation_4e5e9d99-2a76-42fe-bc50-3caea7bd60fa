Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.1f1 (7197418f847b) revision 7444289'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-07-30T23:12:45Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.1.1f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker2.log
-srvPort
62220
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [34624]  Target information:

Player connection [34624]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3115704604 [EditorId] 3115704604 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [34624]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3115704604 [EditorId] 3115704604 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [34624]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3115704604 [EditorId] 3115704604 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [34624]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3115704604 [EditorId] 3115704604 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [34624]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3115704604 [EditorId] 3115704604 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [34624] Host joined multi-casting on [***********:54997]...
Player connection [34624] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 3315.72 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.1f1 (7197418f847b)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.1.1f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.7688
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56512
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.007016 seconds.
- Loaded All Assemblies, in 10.184 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.493 seconds
Domain Reload Profiling: 10664ms
	BeginReloadAssembly (9184ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (413ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (94ms)
	LoadAllAssembliesAndSetupDomain (463ms)
		LoadAssemblies (9170ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (459ms)
			TypeCache.Refresh (456ms)
				TypeCache.ScanAssembly (437ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (493ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (424ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (81ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (167ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Launched and connected shader compiler UnityShaderCompiler.exe after 0.10 seconds
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 19.849 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.09 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.959 seconds
Domain Reload Profiling: 21777ms
	BeginReloadAssembly (6860ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (30ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (614ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (78ms)
	LoadAllAssembliesAndSetupDomain (12807ms)
		LoadAssemblies (12225ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (837ms)
			TypeCache.Refresh (703ms)
				TypeCache.ScanAssembly (675ms)
			BuildScriptInfoCaches (112ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1959ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1655ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (275ms)
			ProcessInitializeOnLoadAttributes (777ms)
			ProcessInitializeOnLoadMethodAttributes (568ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 22.80 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 550 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7910 unused Assets / (3.0 MB). Loaded Objects now: 8974.
Memory consumption went from 380.7 MB to 377.7 MB.
Total: 58.093500 ms (FindLiveObjects: 1.185900 ms CreateObjectMapping: 1.238200 ms MarkObjects: 52.440200 ms  DeleteObjects: 3.227800 ms)

========================================================================
Received Import Request.
  Time since last request: 196698.492320 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0bb8680f5440b22a5b6899b8952d9383') in 23.0682046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 24

========================================================================
Received Import Request.
  Time since last request: 435.781726 seconds.
  path: Assets/_Game/Scripts/PlayerController/ShmovementSystem/GrapplingHook/GrapplingHookSystem.cs
  artifactKey: Guid(3547609edaa5e9344b6450350e194986) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/PlayerController/ShmovementSystem/GrapplingHook/GrapplingHookSystem.cs using Guid(3547609edaa5e9344b6450350e194986) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e0134f0df1dd3e0638ca236878820cf3') in 0.0010511 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.80 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (2.9 MB). Loaded Objects now: 9320.
Memory consumption went from 374.2 MB to 371.3 MB.
Total: 123.475100 ms (FindLiveObjects: 1.195600 ms CreateObjectMapping: 1.475100 ms MarkObjects: 117.120400 ms  DeleteObjects: 3.682700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 39.235610 seconds.
  path: Assets/_Game/Scripts/PlayerController/ShmovementSystem/GrapplingHook/GrapplingHookSystem1.cs
  artifactKey: Guid(4a0051fd77f21174bb8603848b87eac7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/PlayerController/ShmovementSystem/GrapplingHook/GrapplingHookSystem1.cs using Guid(4a0051fd77f21174bb8603848b87eac7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0f8c4294b227ef2af534021af95b923b') in 0.0235908 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 22.57 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (2.9 MB). Loaded Objects now: 9321.
Memory consumption went from 374.2 MB to 371.3 MB.
Total: 141.487500 ms (FindLiveObjects: 1.198700 ms CreateObjectMapping: 1.118500 ms MarkObjects: 136.196700 ms  DeleteObjects: 2.971800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 22.58 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (2.9 MB). Loaded Objects now: 9321.
Memory consumption went from 374.2 MB to 371.3 MB.
Total: 516.845100 ms (FindLiveObjects: 1.179400 ms CreateObjectMapping: 1.011600 ms MarkObjects: 511.617000 ms  DeleteObjects: 3.035400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Leak Detected : Persistent allocates 8 individual allocations. To find out more please enable 'Preferences > Jobs > Leak Detection Level > Enabled With Stack Trace' and reproduce the leak again.
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.228 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 22.68 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.551 seconds
Domain Reload Profiling: 3753ms
	BeginReloadAssembly (665ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (79ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (289ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (66ms)
	LoadAllAssembliesAndSetupDomain (1396ms)
		LoadAssemblies (1167ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (412ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (363ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1551ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1113ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (271ms)
			ProcessInitializeOnLoadAttributes (647ms)
			ProcessInitializeOnLoadMethodAttributes (158ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (49ms)
Refreshing native plugins compatible for Editor in 23.90 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9078.
Memory consumption went from 323.3 MB to 320.4 MB.
Total: 21.398200 ms (FindLiveObjects: 1.283500 ms CreateObjectMapping: 0.996200 ms MarkObjects: 15.911800 ms  DeleteObjects: 3.205300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1994.057198 seconds.
  path: Assets/_Game/Resources/ItemsIcons/AxeName.png
  artifactKey: Guid(e313fba2d45416d43a83bb9ed7202f28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/AxeName.png using Guid(e313fba2d45416d43a83bb9ed7202f28) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e0d6f26bd758244ffea8970abb031849') in 0.1641193 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.250270 seconds.
  path: Assets/_Game/Resources/Items/AxeManual.asset
  artifactKey: Guid(1abe8ea745304c345a70a6ea6cff2775) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/AxeManual.asset using Guid(1abe8ea745304c345a70a6ea6cff2775) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '49a7015a6f51b1138b07e3906e969b78') in 0.0048011 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 2.151059 seconds.
  path: Assets/_Game/Models/Items/Rope.fbx
  artifactKey: Guid(833fcf47655959f45ad7eb379b4ad798) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Items/Rope.fbx using Guid(833fcf47655959f45ad7eb379b4ad798) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '29ce451cb48e9523f9d4d17547b75cc2') in 2.0901966 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 36.639051 seconds.
  path: Assets/_Game/Models/Items/Rope.fbx
  artifactKey: Guid(833fcf47655959f45ad7eb379b4ad798) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Items/Rope.fbx using Guid(833fcf47655959f45ad7eb379b4ad798) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '409e0741b816eaa57ce1d3290ed1cbf3') in 0.265256 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 468.473242 seconds.
  path: Assets/_Game/Resources/ItemsIcons/Rope-thumbnail.png
  artifactKey: Guid(b71f72c143e5204409f7a15d31aa3685) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/Rope-thumbnail.png using Guid(b71f72c143e5204409f7a15d31aa3685) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a74b35e0a1263bd4bf277ed0dde3b172') in 0.0384489 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 10.899141 seconds.
  path: Assets/_Game/Resources/ItemsIcons/silhouette 2.png
  artifactKey: Guid(a271626c6396ae140bbb978c54b0516d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/silhouette 2.png using Guid(a271626c6396ae140bbb978c54b0516d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9e7c21a29420e822791d94f5b72d0c90') in 0.0332651 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/_Game/Resources/ItemsIcons/Rope-thumbnail.png
  artifactKey: Guid(b71f72c143e5204409f7a15d31aa3685) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/Rope-thumbnail.png using Guid(b71f72c143e5204409f7a15d31aa3685) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9ee9eaf76421dad1f798cbf63593f40a') in 0.0327092 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 12.268847 seconds.
  path: Assets/_Game/Resources/Items/Rope.asset
  artifactKey: Guid(76e4408dc4a8b0b4eba1569e37d14faf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/Rope.asset using Guid(76e4408dc4a8b0b4eba1569e37d14faf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9bfe4e0490b53f1460f25ccb56e84490') in 0.0042814 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 2.265172 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Axe.prefab
  artifactKey: Guid(ff187aea2d6ae2047b1649c278a31884) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Axe.prefab using Guid(ff187aea2d6ae2047b1649c278a31884) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2286a92b2545384440137ab5d482f294') in 0.0556323 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 36.572908 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5e976718b7634dabf4c0cd43919b0d44') in 0.0251029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 24

========================================================================
Received Import Request.
  Time since last request: 5.427502 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '09d272347a6cb2f1c8090fa179b1ff46') in 0.0264687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 24

========================================================================
Received Import Request.
  Time since last request: 1.345921 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aee02fbe0d5d183969feecc6a7845aae') in 0.0206989 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 24

========================================================================
Received Import Request.
  Time since last request: 3.976310 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fac398c651a9d2e1668a1a7e7f23a809') in 0.0314605 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 7.409893 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f888fc55e8c8c4f5d39f6a96bcdfd067') in 0.0313464 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 3.545672 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ba114c124acf02059cdf06fa0b71ab58') in 0.0287088 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 8.166147 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f6f21b2963bef98df01c50236a620673') in 0.0232527 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 317.343080 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '182a0fa4a5d1943f83c6493bf11ff628') in 0.0059315 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 9.969538 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1136a41a37005954321666f73afa9f97') in 0.0278479 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 2.946899 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c74b426f427acf615ddf8351cbbf59bc') in 0.0052758 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 9.901954 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b9759bd9e944e23fc907d422a6ab5819') in 0.0380019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 3.514048 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c19b36e01fa569a31844374d8336137c') in 0.0301119 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 5.991363 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c70fec9e9ad16eef064b81023e965504') in 0.0274122 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 3.548671 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '46f3ad680e115da782f003d93b033e39') in 0.0243923 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 2.128835 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '072c83f775938be3288dd20f13e79b60') in 0.0342246 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 2.430525 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e5eaf2eed311e0bc824cead6fd2b8d9f') in 0.0435049 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 1.978323 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/Rope.prefab
  artifactKey: Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/Rope.prefab using Guid(0680df7eadb763446b5b7138a7bccd06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2572ae106142e4b12d526b4cf5fc71ef') in 0.0263033 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

