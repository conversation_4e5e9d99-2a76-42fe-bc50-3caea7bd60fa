using UnityEngine;
using Inventory;
using System.Collections;
using KinematicCharacterController.FPS;

public class ToolSelectionController : MonoBehaviour
{
    [SerializeField] private ToolSelectionManager toolSelectionManager;
    [SerializeField] private EquipmentManager equipmentManager;
    [SerializeField] private ToolModelManager toolModelManager;
    
    [Tooltip("Reference to the FPS camera for controlling zoom functionality")]
    [SerializeField] private FPSCharacterCamera fpsCamera;
    
    private ToolSelectionManager.ToolSelectionItem activeTool;
    private Item activeToolItem;
    private GrapplingHookSystem grapplingHookSystem;
    
    void Start()
    {
        if (toolSelectionManager == null)
        {
            toolSelectionManager = GetComponent<ToolSelectionManager>();
        }
        
        if (equipmentManager == null)
        {
            equipmentManager = FindObjectOfType<EquipmentManager>();
        }
        
        if (toolModelManager == null)
        {
            toolModelManager = GetComponent<ToolModelManager>();
            if (toolModelManager == null)
            {
                toolModelManager = FindObjectOfType<ToolModelManager>();
            }
        }
        
        // Get grappling hook system
        grapplingHookSystem = GetComponent<GrapplingHookSystem>();
        if (grapplingHookSystem == null)
        {
            grapplingHookSystem = FindObjectOfType<GrapplingHookSystem>();
        }
        
        // Find camera if not assigned
        if (fpsCamera == null)
        {
            fpsCamera = FindObjectOfType<FPSCharacterCamera>();
        }
        
        // Listen for tool selection changes
        if (toolSelectionManager != null)
        {
            toolSelectionManager.OnToolSelected += OnToolSelected;
        }
    }
    
    private void OnToolSelected(ToolSelectionManager.ToolSelectionItem selectedTool)
    {
        activeTool = selectedTool;
        activeToolItem = selectedTool.originalItem;
        
        // ToolModelManager will handle the model and animation changes automatically
        // through its own event handler for OnToolSelected
        
        bool isGrapplingHookSelected = false;
        bool isHandSelected = false;
        
        // Check what type of tool is selected
        if (activeToolItem != null)
        {
            var typeField = activeToolItem.GetType().GetField("Type");
            if (typeField != null && typeField.FieldType.IsEnum)
            {
                object toolType = typeField.GetValue(activeToolItem);
                string typeName = toolType.ToString();
                
                // Check for grappling hook
                isGrapplingHookSelected = typeName.Contains("GrapplingHookMk1");
                
                // Check for hand/empty tool
                isHandSelected = typeName.Contains("Hand") || typeName.Contains("Empty");
            }
        }
        else
        {
            // If no tool is selected, consider it as hand selected
            isHandSelected = true;
        }
        
        // Enable or disable grappling hook prediction based on selection
        if (grapplingHookSystem != null)
        {
            grapplingHookSystem.SetPredictionEnabled(isGrapplingHookSelected);
        }
        
        // Enable or disable zoom capability based on hand selection
        if (fpsCamera != null)
        {
            fpsCamera.SetCanZoom(isHandSelected);
        }
    }
    
    void Update()
    {
        // Handle tool usage input (left mouse click)
        if (Input.GetMouseButtonDown(0) && activeTool != null)
        {
            UseTool();
        }
        
        // Release grappling hook with right mouse click
        if (Input.GetMouseButtonDown(1) && activeTool != null)
        {
            ReleaseToolAction();
        }
    }
    
    private void UseTool()
    {
        // Play use animation via the ToolModelManager
        if (toolModelManager != null)
        {
            toolModelManager.PlayUseAnimation();
        }
        
        // This is where you would implement the tool usage functionality
        Debug.Log($"Using tool: {activeTool.toolName}");
        
        // Get the Type field if it exists
        if (activeToolItem != null)
        {
            var typeField = activeToolItem.GetType().GetField("Type");
            if (typeField != null && typeField.FieldType.IsEnum)
            {
                object toolType = typeField.GetValue(activeToolItem);
                Debug.Log($"Tool type: {toolType}");
                
                // Handle different tool types
                string typeName = toolType.ToString();
                
                if (typeName.Contains("ClimbingAxe"))
                {
                    // Climbing axe logic
                    Debug.Log("Using climbing axe");
                }
                else if (typeName.Contains("GrapplingHookMk1"))
                {
                    // Grappling hook logic - release if already swinging, fire if not
                    if (grapplingHookSystem != null)
                    {
                        if (grapplingHookSystem.IsSwinging)
                        {
                            Debug.Log("Releasing grappling hook (was swinging)");
                            grapplingHookSystem.ReleaseGrapple();
                        }
                        else
                        {
                            Debug.Log("Firing grappling hook");
                            grapplingHookSystem.FireGrapple();
                        }
                    }
                }
            }
        }
    }
    
    private void ReleaseToolAction()
    {
        // Get the Type field if it exists
        if (activeToolItem != null)
        {
            var typeField = activeToolItem.GetType().GetField("Type");
            if (typeField != null && typeField.FieldType.IsEnum)
            {
                object toolType = typeField.GetValue(activeToolItem);
                string typeName = toolType.ToString();
                
                if (typeName.Contains("GrapplingHookMk1"))
                {
                    // Release grappling hook - only works if player has the hook selected - only works if player has the hook selected
                    if (grapplingHookSystem != null)
                    {
                        grapplingHookSystem.ReleaseGrapple();
                    }
                }
            }
        }
    }
    
    private void OnDestroy()
    {
        // Clean up event listener
        if (toolSelectionManager != null)
        {
            toolSelectionManager.OnToolSelected -= OnToolSelected;
        }
    }
} 