using UnityEngine;
using System.Collections;
using KinematicCharacterController.FPS;
using AudioSystem;

public class GrapplingHookSystem : MonoBehaviour
{
    [Header("References")]
    [SerializeField] private Transform gunTip;
    [SerializeField] private Transform camera;
    [SerializeField] private Transform player;
    [SerializeField] private LayerMask grapplableMask;
    [SerializeField] private LineRenderer lineRenderer;
    [SerializeField] private Transform predictionPoint;
    [SerializeField] private FPSCharacterController playerMovement;
    [SerializeField] private PredictionVisualizer predictionVisualizer;
    
    // Audio reference
    private PlayerAudioHandler playerAudioHandler;

    [Header("Swinging")]
    [SerializeField] private float maxSwingDistance = 25f;
    [SerializeField] private float swingForce = 4f;
    [SerializeField] private float swingSpeed = 10f;

    [Header("Grapple Physics")]
    [SerializeField] private float swingGravity = 5f;
    [SerializeField] private float ropeSpringForce = 50f;
    [SerializeField] private float maxRopeTension = 100f;
    [SerializeField] private float ropeDamping = 5f;
    [SerializeField] private float maxDistanceMultiplier = 0.8f;
    [SerializeField] private float minDistanceMultiplier = 0.25f;
    [SerializeField] private float ropeSlack = 0.5f;
    [SerializeField, Range(0f, 1f)] private float gravityScaleWhileSwinging = 1f;

    [Header("Air Control")]
    [SerializeField] private float horizontalThrustForce = 4f;
    [SerializeField] private float forwardThrustForce = 2f;
    [SerializeField] private float extendCableSpeed = 10f;
    [SerializeField] private float shortenCableSpeed = 10f;

    [Header("Prediction")]
    [SerializeField] private float predictionSphereCastRadius = 2f;
    [SerializeField] private Material predictionMaterial;

    [Header("Sphere Visualization")]
    [SerializeField] private float baseSphereSize = 0.3f;
    [SerializeField] private Color sphereColor = Color.red;
    [SerializeField] private float distanceScaleFactor = 0.05f;
    [SerializeField] private float positionSmoothTime = 0.1f;

    private Vector3 swingPoint;
    private float currentGrappleDistance;
    private Vector3 currentGrapplePosition;
    private Vector3 lastFramePosition;

    private RaycastHit predictionHit;
    private bool predictionFound;

    private bool isSwinging = false;
    public bool IsSwinging { get { return isSwinging; } }

    private bool isPredictionEnabled = false;

    private float originalMaxSpeed;
    private float originalGravity;

    private MeshRenderer sphereRenderer;
    private Material sphereMaterial;

    private Vector3 targetSpherePosition;
    private Vector3 currentSphereVelocity = Vector3.zero;

    private void Awake()
    {
        if (playerMovement == null)
            playerMovement = GetComponent<FPSCharacterController>();

        if (camera == null && Camera.main != null)
            camera = Camera.main.transform;

        if (player == null)
            player = transform;

        if (lineRenderer == null)
        {
            lineRenderer = gameObject.AddComponent<LineRenderer>();
            SetupLineRenderer();
        }

        if (predictionVisualizer == null)
            predictionVisualizer = GetComponent<PredictionVisualizer>();

        // Get PlayerAudioHandler reference
        if (playerAudioHandler == null)
            playerAudioHandler = GetComponent<PlayerAudioHandler>();

        if (predictionPoint == null)
        {
            CreatePredictionSphere();
        }

        lastFramePosition = transform.position;
    }

    private void CreatePredictionSphere()
    {
        GameObject sphereObj = new GameObject("GrapplePredictionSphere");
        predictionPoint = sphereObj.transform;

        GameObject sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        sphere.transform.SetParent(predictionPoint);
        sphere.transform.localPosition = Vector3.zero;
        sphere.transform.localScale = Vector3.one * baseSphereSize;

        sphereRenderer = sphere.GetComponent<MeshRenderer>();

        if (predictionMaterial != null)
        {
            sphereMaterial = new Material(predictionMaterial);
        }
        else
        {
            sphereMaterial = new Material(Shader.Find("Standard"));
            sphereMaterial.EnableKeyword("_EMISSION");
            sphereMaterial.SetColor("_EmissionColor", sphereColor * 2.0f);
        }

        sphereMaterial.color = sphereColor;
        sphereRenderer.material = sphereMaterial;

        Destroy(sphere.GetComponent<Collider>());

        sphereObj.SetActive(false);

        targetSpherePosition = predictionPoint.position;
    }

    private void UpdateSphereVisualization(RaycastHit hit)
    {
        if (predictionPoint == null) return;

        targetSpherePosition = hit.point + hit.normal * 0.05f;

        predictionPoint.position = Vector3.SmoothDamp(
            predictionPoint.position,
            targetSpherePosition,
            ref currentSphereVelocity,
            positionSmoothTime
        );

        float distance = Vector3.Distance(camera.position, hit.point);
        float adaptiveSize = baseSphereSize + (distance * distanceScaleFactor);
        adaptiveSize = Mathf.Clamp(adaptiveSize, baseSphereSize * 0.5f, baseSphereSize * 3f);

        predictionPoint.localScale = Vector3.one * adaptiveSize;
    }

    private void SetupLineRenderer()
    {
        lineRenderer.startWidth = 0.1f;
        lineRenderer.endWidth = 0.1f;
        lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
        lineRenderer.startColor = Color.black;
        lineRenderer.endColor = Color.black;
        lineRenderer.positionCount = 0;
    }

    private void Update()
    {
        if (isPredictionEnabled)
        {
            if (predictionVisualizer != null)
            {
                predictionFound = predictionVisualizer.HasPredictionPoint();
                if (predictionFound)
                    predictionHit = predictionVisualizer.GetPredictionHit();
            }
            else
            {
                CheckForSwingPoint();
            }
        }
        else
        {
            if (predictionPoint != null)
            {
                predictionPoint.gameObject.SetActive(false);
            }
            predictionFound = false;
        }

        if (isSwinging)
        {
            HandleSwinging();
            HandleSwingAirControl();
        }

        lastFramePosition = transform.position;
    }

    private void LateUpdate()
    {
        DrawRope();
    }

    public void FireGrapple()
    {
        if (isSwinging) return;

        bool canGrapple = false;
        Vector3 grappleTarget = Vector3.zero;

        if (predictionVisualizer != null && predictionVisualizer.HasPredictionPoint())
        {
            canGrapple = true;
            grappleTarget = predictionVisualizer.GetPredictionPoint();
        }
        else if (predictionFound)
        {
            canGrapple = true;
            grappleTarget = predictionHit.point;
        }

        if (canGrapple)
        {
            // Play the grapple fire sound BEFORE setting isSwinging
            if (playerAudioHandler != null)
            {
                playerAudioHandler.OnGrappleFire();
            }
            
            isSwinging = true;
            swingPoint = grappleTarget;
            currentGrappleDistance = Vector3.Distance(player.position, swingPoint) + ropeSlack;
            StartSwing();
        }
    }

    public void ReleaseGrapple()
    {
        if (isSwinging)
        {
            StopSwing();
        }
    }

    private void CheckForSwingPoint()
    {
        if (isSwinging || !isPredictionEnabled) return;

        predictionFound = false;

        RaycastHit raycastHit;
        bool rayHit = Physics.Raycast(camera.position, camera.forward, out raycastHit, maxSwingDistance, grapplableMask);

        RaycastHit spherecastHit;
        bool sphereHit = Physics.SphereCast(camera.position, predictionSphereCastRadius, camera.forward,
            out spherecastHit, maxSwingDistance, grapplableMask);

        if (rayHit)
        {
            predictionHit = raycastHit;
            predictionFound = true;
        }
        else if (sphereHit)
        {
            predictionHit = spherecastHit;
            predictionFound = true;
        }

        if (predictionPoint != null)
        {
            if (predictionFound)
            {
                if (!predictionPoint.gameObject.activeSelf)
                {
                    predictionPoint.gameObject.SetActive(true);
                    predictionPoint.position = predictionHit.point + predictionHit.normal * 0.05f;
                    targetSpherePosition = predictionPoint.position;
                    currentSphereVelocity = Vector3.zero;
                }
                UpdateSphereVisualization(predictionHit);
            }
            else
            {
                predictionPoint.gameObject.SetActive(false);
            }
        }
    }

    private void StartSwing()
    {
        originalMaxSpeed = playerMovement.MaxStableMoveSpeed;
        originalGravity = playerMovement.Gravity.y;

        SetPlayerSwingingState(true);

        lineRenderer.positionCount = 2;
        currentGrapplePosition = gunTip.position;

        currentGrappleDistance = Vector3.Distance(player.position, swingPoint) + ropeSlack;

        if (predictionPoint != null)
            predictionPoint.gameObject.SetActive(false);
    }

    private void StopSwing()
    {
        isSwinging = false;

        SetPlayerSwingingState(false);

        lineRenderer.positionCount = 0;
    }

    private void HandleSwinging()
    {
        if (!playerMovement.Motor.GroundingStatus.IsStableOnGround)
        {
            float distanceToGrapplePoint = Vector3.Distance(player.position, swingPoint);

            Vector3 directionToGrapplePoint = (swingPoint - player.position).normalized;

            float ropeStretchAmount = Mathf.Max(0, distanceToGrapplePoint - currentGrappleDistance);

            float tensionForce = ropeStretchAmount * ropeSpringForce;

            Vector3 playerVelocity = (player.position - lastFramePosition) / Time.deltaTime;
            float velocityInRopeDirection = Vector3.Dot(playerVelocity, directionToGrapplePoint);

            if (velocityInRopeDirection < 0)
            {
                tensionForce += -velocityInRopeDirection * ropeDamping;
            }

            tensionForce = Mathf.Min(tensionForce, maxRopeTension);

            if (ropeStretchAmount > 0)
            {
                Vector3 forceToApply = directionToGrapplePoint * tensionForce * Time.deltaTime;
                playerMovement.AddVelocity(forceToApply);
            }
        }
    }

    private void HandleSwingAirControl()
    {
        if (!isSwinging) return;

        if (!playerMovement.Motor.GroundingStatus.IsStableOnGround)
        {
            if (Input.GetKey(KeyCode.A))
                AddAirControlForce(-camera.right);

            if (Input.GetKey(KeyCode.D))
                AddAirControlForce(camera.right);

            if (Input.GetKey(KeyCode.W))
                AddAirControlForce(camera.forward);

            if (Input.GetKey(KeyCode.Space))
            {
                Vector3 directionToPoint = (swingPoint - transform.position).normalized;
                playerMovement.AddVelocity(directionToPoint * swingForce * Time.deltaTime);

                currentGrappleDistance -= shortenCableSpeed * Time.deltaTime;
            }

            if (Input.GetKey(KeyCode.S))
            {
                currentGrappleDistance += extendCableSpeed * Time.deltaTime;
            }
        }
    }

    private void AddAirControlForce(Vector3 direction)
    {
        float forceMultiplier = (direction == camera.forward) ? forwardThrustForce : horizontalThrustForce;
        playerMovement.AddVelocity(direction * forceMultiplier * Time.deltaTime);
    }

    private void DrawRope()
    {
        if (!isSwinging || lineRenderer.positionCount != 2) return;

        currentGrapplePosition = Vector3.Lerp(currentGrapplePosition, swingPoint, Time.deltaTime * swingSpeed);

        lineRenderer.SetPosition(0, gunTip.position);
        lineRenderer.SetPosition(1, currentGrapplePosition);
    }

    private void SetPlayerSwingingState(bool isSwinging)
    {
        if (isSwinging)
        {
            if (!playerMovement.Motor.GroundingStatus.IsStableOnGround)
            {
                playerMovement.MaxStableMoveSpeed = 30f;
            }

            playerMovement.Gravity = new Vector3(0, originalGravity * gravityScaleWhileSwinging, 0);
        }
        else
        {
            playerMovement.MaxStableMoveSpeed = originalMaxSpeed;
            playerMovement.Gravity = new Vector3(0, originalGravity, 0);
        }
    }

    private void OnDisable()
    {
        StopSwing();
    }

    public void SetPredictionEnabled(bool enabled)
    {
        isPredictionEnabled = enabled;

        if (!enabled && predictionPoint != null)
        {
            predictionPoint.gameObject.SetActive(false);
        }
    }
}